import 'package:flutter/material.dart';
import 'package:stream_video_flutter/stream_video_flutter.dart';

class CallView extends StatefulWidget {
  final Call call;
final String userId;
   const CallView({super.key, required this.call, required this.userId});

  @override
  State<CallView> createState() => _CallViewState();
}

class _CallViewState extends State<CallView> {
  @override
  void initState() {
    super.initState();
    // Initialize StreamVideo with your API key
    streamVideo = StreamVideo(
      apiKey: 'YOUR_API_KEY',
      user: UserInfo(id: widget.userId),
    );

    // Join predefined room
    call = streamVideo.makeCall(type: 'default', id: 'test-room');

    _joinCall();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: StreamCallContainer(
        call: widget.call,
      ),
    );
  }
}