import 'package:stream_video_flutter/stream_video_flutter.dart';
import 'package:task_guitara_group/core/constants.dart';
import 'package:task_guitara_group/core/token_generator.dart';

class StreamVideoService {
  static StreamVideo? _instance;
  static String? _currentUserId;

  /// Get or create StreamVideo instance
  static Future<StreamVideo> getInstance({
    required String userId,
    required String userName,
  }) async {
    // If we have an instance and it's for the same user, return it
    if (_instance != null && _currentUserId == userId) {
      return _instance!;
    }

    // If we have an instance but for a different user, disconnect first
    if (_instance != null && _currentUserId != userId) {
      try {
        await _instance!.disconnect();
      } catch (e) {
        // Ignore disconnect errors
      }
      _instance = null;
    }

    // Create new instance
    final user = User.regular(userId: userId, name: userName);
    
    final token = TokenGenerator.generateDevelopmentToken(
      apiSecret: apiSecret,
      userId: userId,
    );

    _instance = StreamVideo(a<PERSON><PERSON><PERSON>, user: user, userToken: token);
    _currentUserId = userId;

    return _instance!;
  }

  /// Reset the StreamVideo instance
  static Future<void> reset() async {
    if (_instance != null) {
      try {
        await _instance!.disconnect();
      } catch (e) {
        // Ignore disconnect errors
      }
      _instance = null;
      _currentUserId = null;
    }
  }

  /// Check if instance exists
  static bool get hasInstance => _instance != null;

  /// Get current user ID
  static String? get currentUserId => _currentUserId;
}
